#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from contextlib import contextmanager
from icecream import ic
import logging
import os
import pytest
import re
from typing import Any, cast, Dict, Union

from logear.autolog_exception import gen_logger_name_by_callable, get_logger_by_callable, adapt_autolog_exception
from .test_autolog_exception import get_log_file_from_logger, read_log_file, verify_logfile


class TestAdaptAutoLogException:
    @contextmanager
    def setup_logging_context(self, caplog, log_file_name: str, exception_obj: Exception):
        with open(log_file_name, 'rt') as fd:
            lines = fd.readlines()
        assert len(lines) < 1, \
            "TEST ERROR: should no log yet existing in log file {0}, but actual already does: {1}".format(
                log_file_name, lines)

        is_ic_enabled = ic.enabled
        if is_ic_enabled:
            ic.disable()

        with caplog.at_level(level=logging.ERROR):
            with pytest.raises(type(exception_obj), match=exception_obj.args[0]):
                yield

        if is_ic_enabled:
            ic.enable()

    @pytest.mark.run(order=1)
    def test_adapt_autolog_exception_for_bad_method(self, caplog, capsys):
        exception_obj = RuntimeError("Test exception object in {0} test method".format(
            self.test_adapt_autolog_exception_for_bad_method.__name__))

        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            def bad_method(self):
                raise exception_obj

            def regular_method(self) -> str:
                return 'regular_method'

        instance = AutoLogExceptionAdapted()
        variable_name = [k for k, v in locals().items() if v is instance][0]

        logger = get_logger_by_callable(callee=instance.bad_method)
        log_file_name = get_log_file_from_logger(
            logger=logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(instance.__module__,
                                                                type(instance).__qualname__,
                                                                hex(hash(instance)),
                                                                os.path.extsep))

        try:
            with self.setup_logging_context(caplog, log_file_name=log_file_name, exception_obj=exception_obj):
                instance.bad_method()
        finally:
            captured = capsys.readouterr()

        assert re.match(pattern="^.+ - {0} - {1} - {2}".format(
            logger.name, 'ERROR', exception_obj.args[0]), string=captured.err)

        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(instance.__module__,
                                                                                       type(instance).__qualname__,
                                                                                       hex(hash(instance)),
                                                                                       'ERROR',
                                                                                       exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format(variable_name, instance.bad_method.__name__))))

        assert instance.regular_method() == 'regular_method'
        assert len(lines) == len(read_log_file(log_file_name=log_file_name))

    @classmethod
    def gen_exception(cls, obj: Any) -> RuntimeError:
        return RuntimeError("Test exception object in {0}.{1}_{2}".format(
            obj.__module__, type(obj).__qualname__, hex(hash(obj))))

    def test_adapt_autolog_exception_for_overriding_with_decorated_one(self, caplog, capsys):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def bad_method1(self):
                raise self.exception_obj

        @adapt_autolog_exception()
        class ChildAutoLogExceptionAdapted(AutoLogExceptionAdapted):
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def bad_method1(self):
                raise self.exception_obj

        child_subject = ChildAutoLogExceptionAdapted()
        child_logger = get_logger_by_callable(callee=child_subject.bad_method1)
        with pytest.raises(type(child_subject.exception_obj), match=child_subject.exception_obj.args[0]):
            child_subject.bad_method1()

        log_file_name = get_log_file_from_logger(
            logger=child_logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(child_subject.__module__,
                                                                type(child_subject).__qualname__,
                                                                hex(hash(child_subject)), os.path.extsep))
        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=child_subject.exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(
                           child_subject.__module__,
                           type(child_subject).__qualname__,
                           hex(hash(child_subject)),
                           'ERROR',
                           child_subject.exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format('child_subject', child_subject.bad_method1.__name__))))

    def test_adapt_autolog_exception_for_not_overridden_method(self, caplog, capsys):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def bad_method1(self):
                raise self.exception_obj

            def bad_method2(self):
                raise self.exception_obj

        @adapt_autolog_exception()
        class ChildAutoLogExceptionAdapted(AutoLogExceptionAdapted):
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def bad_method1(self):
                raise self.exception_obj

        child_subject = ChildAutoLogExceptionAdapted()
        child_logger = get_logger_by_callable(callee=child_subject.bad_method2)
        with pytest.raises(type(child_subject.exception_obj), match=child_subject.exception_obj.args[0]):
            child_subject.bad_method2()

        log_file_name = get_log_file_from_logger(
            logger=child_logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(child_subject.__module__,
                                                                type(child_subject).__qualname__,
                                                                hex(hash(child_subject)), os.path.extsep))
        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=child_subject.exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(
                           child_subject.__module__,
                           type(child_subject).__qualname__,
                           hex(hash(child_subject)),
                           'ERROR',
                           child_subject.exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format('child_subject', child_subject.bad_method2.__name__))))

    def test_adapt_autolog_exception_for_calling_super_method(self, caplog, capsys):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def bad_method1(self):
                raise self.exception_obj

        class ChildAutoLogExceptionAdapted(AutoLogExceptionAdapted):
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def call_super_bad_method1(self):
                self.bad_method1()

            def bad_method2(self):
                raise self.exception_obj

        child_subject = ChildAutoLogExceptionAdapted()
        child_logger = get_logger_by_callable(callee=child_subject.bad_method2)
        with pytest.raises(type(child_subject.exception_obj), match=child_subject.exception_obj.args[0]):
            child_subject.bad_method2()

        log_file_name = get_log_file_from_logger(
            logger=child_logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(child_subject.__module__,
                                                                type(child_subject).__qualname__,
                                                                hex(hash(child_subject)), os.path.extsep))
        with open(log_file_name, 'rt') as fd:
            lines = fd.readlines()
        assert len(lines) < 1, "Expected to find no log in log file {0}, but actual not: {1}".format(log_file_name, lines)

        with pytest.raises(type(child_subject.exception_obj), match=child_subject.exception_obj.args[0]):
            child_subject.call_super_bad_method1()

        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=child_subject.exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(
                           child_subject.__module__,
                           type(child_subject).__qualname__,
                           hex(hash(child_subject)),
                           'ERROR',
                           child_subject.exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format('self', child_subject.bad_method1.__name__))))

    def test_adapt_autolog_exception_for_bad_static_method(self, caplog, capsys):
        exception_obj = RuntimeError("Test exception object in {0} test method".format(
            self.test_adapt_autolog_exception_for_bad_static_method.__name__))

        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            @staticmethod
            def bad_static_method():
                raise exception_obj

            @staticmethod
            def regular_static_method():
                return "regular_static_method"

        logger = get_logger_by_callable(callee=AutoLogExceptionAdapted.bad_static_method)
        log_file_name = get_log_file_from_logger(
            logger=logger,
            regex_pattern="{0}.{1}.[0-9a-z_]+{2}log".format(AutoLogExceptionAdapted.__module__,
                                                            AutoLogExceptionAdapted.__qualname__,
                                                            os.path.extsep))

        try:
            with self.setup_logging_context(caplog, log_file_name=log_file_name, exception_obj=exception_obj):
                AutoLogExceptionAdapted.bad_static_method()
        finally:
            captured = capsys.readouterr()

        assert re.match(pattern="^.+ - {0} - {1} - {2}".format(
            logger.name, 'ERROR', exception_obj.args[0]), string=captured.err)

        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1} - {2} - {3}".format(AutoLogExceptionAdapted.__module__,
                                                                                   AutoLogExceptionAdapted.__qualname__,
                                                                                   'ERROR',
                                                                                   exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format(AutoLogExceptionAdapted.__name__,
                                                        AutoLogExceptionAdapted.bad_static_method.__name__))))

        assert AutoLogExceptionAdapted.regular_static_method() == "regular_static_method"
        assert len(lines) == len(read_log_file(log_file_name=log_file_name))

    def test_adapt_autolog_exception_for_overriding_static_bad_method(self, caplog, capsys):
        exception_obj = RuntimeError("Test exception object in {0} test method".format(
            self.test_adapt_autolog_exception_for_overriding_static_bad_method.__name__))

        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            @staticmethod
            def bad_static_method():
                raise exception_obj

        @adapt_autolog_exception()
        class ChildAutoLogExceptionAdapted(AutoLogExceptionAdapted):
            @staticmethod
            def bad_static_method():
                raise exception_obj

        child_logger = get_logger_by_callable(callee=ChildAutoLogExceptionAdapted.bad_static_method)
        with pytest.raises(type(exception_obj), match=exception_obj.args[0]):
            ChildAutoLogExceptionAdapted.bad_static_method()

        log_file_name = get_log_file_from_logger(
            logger=child_logger,
            regex_pattern="{0}.{1}.[0-9a-z_]+{2}log".format(ChildAutoLogExceptionAdapted.__module__,
                                                            ChildAutoLogExceptionAdapted.__qualname__,
                                                            os.path.extsep))
        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1} - {2} - {3}".format(
                           ChildAutoLogExceptionAdapted.__module__,
                           ChildAutoLogExceptionAdapted.__qualname__,
                           'ERROR',
                           exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format(ChildAutoLogExceptionAdapted.__name__,
                                                        ChildAutoLogExceptionAdapted.bad_static_method.__name__))))

    def test_adapt_autolog_exception_for_bad_static_method_on_instance(self, caplog, capsys):
        exception_obj = RuntimeError("Test exception object in {0} test method".format(
            self.test_adapt_autolog_exception_for_bad_static_method_on_instance.__name__))

        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            @staticmethod
            def bad_static_method(x, y=1):
                raise exception_obj

            @staticmethod
            def regular_static_method(x, y=1) -> Dict:
                return {'x': x, 'y': y}

        instance = AutoLogExceptionAdapted()

        logger = get_logger_by_callable(callee=AutoLogExceptionAdapted.bad_static_method)
        log_file_name = get_log_file_from_logger(
            logger=logger,
            regex_pattern="{0}.{1}.[0-9a-z_]+{2}log".format(AutoLogExceptionAdapted.__module__,
                                                                AutoLogExceptionAdapted.__qualname__,
                                                                os.path.extsep))
        try:
            with self.setup_logging_context(
                    caplog, log_file_name=log_file_name, exception_obj=exception_obj):
                instance.bad_static_method(x=1, y=2)
        finally:
            captured = capsys.readouterr()

        assert re.match(pattern="^.+ - {0} - {1} - {2}".format(
            logger.name, 'ERROR', exception_obj.args[0]), string=captured.err)

        lines = read_log_file(log_file_name=log_file_name)
        instance_name = {v:k for k,v in locals().items() if v == instance}.get(instance, None)
        assert instance_name is not None, "TEST ERROR: Failed to find instance name in locals."

        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1} - {2} - {3}".format(
                           AutoLogExceptionAdapted.__module__,
                           AutoLogExceptionAdapted.__qualname__,
                           'ERROR',
                           exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}".format(instance_name,
                                                        AutoLogExceptionAdapted.bad_static_method.__name__))))

        kwargs = {'x': 1, 'y': 2}
        with caplog.at_level(level=logging.ERROR):
            assert kwargs == instance.regular_static_method(*list(kwargs.values()))

        updated_liens = read_log_file(log_file_name=log_file_name)
        assert len(lines) == len(updated_liens)

    def test_adapt_autolog_exception_for_inherited_static_method(self):
        """Test case for inherited staticmethod where cls.__dict__.get(name) returns None.

        This test focuses on reproducing the AttributeError that occurs when the decorator
        tries to process an inherited staticmethod and cls.__dict__.get(name) returns None.
        """

        @adapt_autolog_exception()
        class ParentAutoLogExceptionAdapted:
            @staticmethod
            def inherited_static_method():
                return "inherited_static_method_result"

        # This should trigger the bug where cls.__dict__.get(name) returns None
        # for inherited staticmethods, causing AttributeError: 'NoneType' object has no attribute '__name__'
        @adapt_autolog_exception()
        class ChildAutoLogExceptionAdapted(ParentAutoLogExceptionAdapted):
            # Child class doesn't override the staticmethod - it's inherited
            pass

        # If we get here without an AttributeError, the fix is working
        # Test that the inherited staticmethod still works correctly
        result = ChildAutoLogExceptionAdapted.inherited_static_method()
        assert result == "inherited_static_method_result"

    # TODO: add test for console output.
    def test_adapt_autolog_exception_for_bad_property(self, caplog, capsys):
        exception_obj = RuntimeError("Test exception object in {0} test method".format(
            self.test_adapt_autolog_exception_for_bad_property.__name__))

        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            @property
            def bad_property(self):
                raise exception_obj

            @property
            def regular_property(self):
                return 'regular_property'

            def dummy_method(self):
                return 'dummy_method'

        subject = AutoLogExceptionAdapted()

        assert subject.regular_property == 'regular_property'

        logger = get_logger_by_callable(callee=subject.dummy_method)
        log_file_name = get_log_file_from_logger(
            logger=logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(subject.__module__,
                                                                type(subject).__qualname__,
                                                                hex(hash(subject)),
                                                                os.path.extsep))

        try:
            with self.setup_logging_context(caplog, log_file_name=log_file_name, exception_obj=exception_obj):
                subject.bad_property
        finally:
            captured = capsys.readouterr()

        assert re.match(pattern="^.+ - {0} - {1} - {2}".format(
            logger.name, 'ERROR', exception_obj.args[0]), string=captured.err)

        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(subject.__module__,
                                                                                       type(subject).__qualname__,
                                                                                       hex(hash(subject)),
                                                                                       'ERROR',
                                                                                       exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}".format('subject', 'bad_property'))))

        assert subject.regular_property == 'regular_property'
        assert len(lines) == len(read_log_file(log_file_name=log_file_name))

    def test_adapt_autolog_exception_for_overriding_bad_property(self, caplog, capsys):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            @property
            def bad_property1(self):
                raise self.exception_obj

        @adapt_autolog_exception()
        class ChildAutoLogExceptionAdapted(AutoLogExceptionAdapted):
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            @property
            def bad_property1(self):
                raise self.exception_obj

            def dummy_method(self):
                return 'dummy_method'

        child_subject = ChildAutoLogExceptionAdapted()
        with pytest.raises(type(child_subject.exception_obj), match=child_subject.exception_obj.args[0]):
            child_subject.bad_property1

        child_logger = get_logger_by_callable(callee=child_subject.dummy_method)
        log_file_name = get_log_file_from_logger(
            logger=child_logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(child_subject.__module__,
                                                                type(child_subject).__qualname__,
                                                                hex(hash(child_subject)), os.path.extsep))
        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=child_subject.exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(
                           child_subject.__module__,
                           type(child_subject).__qualname__,
                           hex(hash(child_subject)),
                           'ERROR',
                           child_subject.exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}".format('child_subject', 'bad_property1'))))

    def test_adapt_autolog_exception_for_calling_super_bad_property(self, caplog, capsys):
        class AutoLogExceptionAdapted:
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            @property
            def bad_property1(self):
                raise self.exception_obj

        @adapt_autolog_exception()
        class ChildAutoLogExceptionAdapted(AutoLogExceptionAdapted):
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def dummy_method(self):
                return 'dummy_method'

        child_subject = ChildAutoLogExceptionAdapted()
        with pytest.raises(type(child_subject.exception_obj), match=child_subject.exception_obj.args[0]):
            child_subject.bad_property1

        child_logger = get_logger_by_callable(callee=child_subject.dummy_method)
        log_file_name = get_log_file_from_logger(
            logger=child_logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(child_subject.__module__,
                                                                type(child_subject).__qualname__,
                                                                hex(hash(child_subject)), os.path.extsep))
        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=child_subject.exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(
                           child_subject.__module__,
                           type(child_subject).__qualname__,
                           hex(hash(child_subject)),
                           'ERROR',
                           child_subject.exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}".format('child_subject', 'bad_property1'))))


    def test_adapt_autolog_exception_for_logger_property(self, caplog, capsys):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            @property
            def logger(self) -> logging.Logger:
                """
                Returns the logger instance.
                """
                if getattr(self, 'grab_logger', None):
                    return self.grab_logger()
                return logging.getLogger(__name__)

        subject = AutoLogExceptionAdapted()
        assert isinstance(subject.logger, logging.Logger)
        assert cast(logging.Logger, subject.logger).hasHandlers()
        assert cast(logging.Logger, subject.logger).handlers is not None
        assert len(cast(logging.Logger, subject.logger).handlers) > 1

    #TODO: add test with child class what defines only a field attribute and inherit logger property from parent.


    def test_adapt_autolog_exception_for_nested_call(self, caplog: pytest.LogCaptureFixture, capsys: pytest.CaptureFixture):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            exception_obj = TestAdaptAutoLogException.gen_exception(obj=self)

            def bad_inner_method(self):
                raise self.exception_obj

            def call_bad_method(self):
                self.bad_inner_method()

            def inner_method(self, msg:str) -> str:
                return msg

            def call_method(self, msg:str) -> str:
                return self.inner_method(msg=msg)

        subject = AutoLogExceptionAdapted()
        logger = get_logger_by_callable(callee=subject.call_method)
        assert 'test' == subject.call_method(msg='test')
        log_file_name = get_log_file_from_logger(
            logger=logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(subject.__module__,
                                                                type(subject).__qualname__,
                                                                hex(hash(subject)), os.path.extsep))
        with open(log_file_name, 'rt') as fd:
            lines = fd.readlines()
        assert len(lines) < 1, "Expected to find no log in log file {0}, but actual not: {1}".format(
            log_file_name, lines)

        logger = get_logger_by_callable(callee=subject.call_bad_method)
        with pytest.raises(type(subject.exception_obj), match=subject.exception_obj.args[0]):
            subject.call_bad_method()
        log_file_name = get_log_file_from_logger(
            logger=logger,
            regex_pattern="{0}.{1}_{2}.[0-9a-z_]+{3}log".format(subject.__module__,
                                                                type(subject).__qualname__,
                                                                hex(hash(subject)), os.path.extsep))
        lines = read_log_file(log_file_name=log_file_name)
        verify_logfile(log_file_name=log_file_name,
                       lines=lines,
                       exception=subject.exception_obj,
                       first_line_regex_pattern="^.+ - {0}.{1}_{2} - {3} - {4}".format(
                           subject.__module__,
                           type(subject).__qualname__,
                           hex(hash(subject)),
                           'ERROR',
                           subject.exception_obj.args[0]),
                       last_regex_pattern="^[ \t]+{0}".format(
                           re.escape("{0}.{1}()".format('subject', subject.call_bad_method.__name__))))

    def test_grab_logger_on_instance_by_adapt_autolog_exception(self):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            def throwable_function(ret_obj: Union[str, Exception]) -> str:
                if isinstance(ret_obj, str):
                    return ret_obj
                else:
                    raise ret_obj

        subject = AutoLogExceptionAdapted()
        assert callable(getattr(subject, 'grab_logger', None))
        logger = subject.grab_logger()
        assert isinstance(logger, logging.Logger)
        assert logger.name == gen_logger_name_by_callable(subject.throwable_function)
        assert logger.hasHandlers()

    def test_grab_logger_on_class_by_adapt_autolog_exception(self):
        @adapt_autolog_exception()
        class AutoLogExceptionAdapted:
            def throwable_function(ret_obj: Union[str, Exception]) -> str:
                if isinstance(ret_obj, str):
                    return ret_obj
                else:
                    raise ret_obj

        assert callable(getattr(AutoLogExceptionAdapted, 'grab_logger', None))
        logger = AutoLogExceptionAdapted.grab_logger()
        assert isinstance(logger, logging.Logger)
        assert logger.name == gen_logger_name_by_callable(AutoLogExceptionAdapted.throwable_function)
        assert logger.hasHandlers()

    # TODO: add test for custom shall_wrap callable test case.
    # TODO: add test for custom logger in decorated class test case.
    # TODO: add test for calling super method of crossing over between decorated and non-decorated classes.
