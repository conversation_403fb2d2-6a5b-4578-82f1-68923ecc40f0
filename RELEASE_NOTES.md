1.2.1 / 2025-06-02
==================

  * 573d5ab Fix for the case of classmethod execution.

1.2.0 / 2025-05-11
==================

  * 7452d85 Fix grab_logger method to generate logger name not by using collable from parent. Also changed file names and structure.

1.1.6 / 2025-04-21
==================

  * Fix for RecursionError case by nested property reference.

1.1.5 / 2025-04-18
==================

  * Fix to skip async method call in default_shall_wrap function.

1.1.4 / 2024-09-30
==================

  * Fix to handle static method call in adapt_autolog_exception function.

1.1.3 / 2024-09-24
==================

  * Fix to handle static method call on instance instead of class

0.1.2-1 / 2024-06-08
====================

  * Fixed AutoLogException.get_fully_qualified_name function for callable class object case.
